# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "Types": {
                    "unrealCollapsingAPI": {
                        "alias": {
                            "UsdSchemaBase": "CollapsingAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "CollapsingAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealControlRigAPI": {
                        "alias": {
                            "UsdSchemaBase": "ControlRigAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "SkelRoot", 
                            "Skeleton"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "ControlRigAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealGroomAPI": {
                        "alias": {
                            "UsdSchemaBase": "GroomAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "Curves", 
                            "Xform"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "GroomAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealGroomBindingAPI": {
                        "alias": {
                            "UsdSchemaBase": "GroomBindingAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "Mesh", 
                            "SkelRoot"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "GroomBindingAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealLiveLinkAPI": {
                        "alias": {
                            "UsdSchemaBase": "LiveLinkAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "LiveLinkAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealLodSubtreeAPI": {
                        "alias": {
                            "UsdSchemaBase": "LodSubtreeAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "Scope", 
                            "Xform"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "LodSubtreeAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealSparseVolumeTextureAPI": {
                        "alias": {
                            "UsdSchemaBase": "SparseVolumeTextureAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "Volume", 
                            "OpenVDBAsset"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "SparseVolumeTextureAPI", 
                        "schemaKind": "singleApplyAPI"
                    }
                }
            }, 
            "Name": "unreal", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "resource"
        }
    ]
}
