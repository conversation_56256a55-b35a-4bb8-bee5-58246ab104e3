{"Plugins": [{"Info": {"Validators": {"EncapsulationRulesValidator": {"doc": "Connectable prims (e.g. Shader, Material, etc) can only be nested inside other Container-like Connectable prims. Container-like prims include Material, NodeGraph, Light, LightFilter. Shader is not a Container-like prim."}, "MaterialBindingApiAppliedValidator": {"doc": "Verify a prim has the MaterialBindingAPI applied if it has a material binding relationship."}, "MaterialBindingCollectionValidator": {"doc": "Verify that a collection defining a material binding is well-formed", "schemaTypes": ["MaterialBindingAPI"]}, "MaterialBindingRelationships": {"doc": "All properties named 'material:binding' or in that namespace should be relationships."}, "NormalMapTextureValidator": {"doc": "UsdUVTexture nodes that feed the _inputs:normals_ of a UsdPreviewSurface must ensure that the data is encoded and scaled properly. Specifically, since normals are expected to be in the range [(-1,-1,-1), (1,1,1)], the Texture node must transform 8-bit textures from their [0..1] range by setting its _inputs:scale_ to (2, 2, 2, 1) and _inputs:bias_ to (-1, -1, -1, 0). Normal map data is commonly expected to be linearly encoded.  However, many image-writing tools automatically set the profile of three-channel, 8-bit images to SRGB.  To prevent an unwanted transformation, the UsdUVTexture's _inputs:sourceColorSpace_ must be set to raw.", "schemaTypes": ["UsdShadeS<PERSON>er"]}, "ShaderSdrCompliance": {"doc": "Shader prim's input types must be conforming to their appropriate sdf types in the respective sdr shader.", "schemaTypes": ["UsdShadeS<PERSON>er"]}, "SubsetMaterialBindFamilyName": {"doc": "Geom subsets with authored material bindings should have the 'materialBind' family name.", "keywords": ["UsdGeomSubset"], "schemaTypes": ["UsdGeomSubset"]}, "SubsetsMaterialBindFamily": {"doc": "Geom subsets of the 'materialBind' family should have a restricted family type.", "keywords": ["UsdGeomSubset"], "schemaTypes": ["UsdGeomImageable"]}, "keywords": ["UsdShadeValidators"]}}, "LibraryPath": "../../../../../Source/ThirdParty/Mac/bin/libusd_usdShadeValidators.dylib", "Name": "usdShadeValidators", "ResourcePath": "resources", "Root": "..", "Type": "library"}]}