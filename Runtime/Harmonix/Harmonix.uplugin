{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Harmonix", "Description": "A package of Harmonix music related audio functionality.", "Category": "Audio", "CreatedBy": "Epic Games, Inc. - Harmonix GenTech", "CreatedByURL": "", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "Modules": [{"Name": "Harmonix", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "HarmonixEditor", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "HarmonixDsp", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "HarmonixDspTests", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "HarmonixDspEditor", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "HarmonixMidi", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "HarmonixMidiEditor", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "HarmonixMidiTests", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "HarmonixMetasound", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "HarmonixMetasoundEditor", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "HarmonixMetasoundTests", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "Metasound", "Enabled": true}, {"Name": "MusicEnvironment", "Enabled": true}, {"Name": "EditorScriptingUtilities", "Enabled": true}]}