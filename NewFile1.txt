# AI规则书
---
### 智能开发工作流

#### ### 工作流设计原则
- AI-First：以AI能力为核心驱动开发流程
- 质量内建：在每个阶段嵌入质量保证机制
- 持续反馈：建立闭环的学习和改进机制
- 风险前置：提前识别和缓解潜在风险

**集成工具**：
- 任务编排：shrimp-task-manager (任务编排与协调)
- 知识管理：OpenMemory MCP (知识管理与上下文)
- 执行引擎：Zen MCP Server (分析与决策支持)

#### ### 阶段1：智能项目洞察与分析 
**目标**：建立项目全景认知和技术基线，识别关键技术风险和机会点，制定技术策略和架构原则

**AI协作模式**：
1. shrimp-task-manager：项目范围分析和分解 → 结构化的分析任务清单
2. OpenMemory MCP：历史项目经验和最佳实践检索 → 相关技术栈经验和决策历史
3. Zen MCP Server：深度技术分析和架构评估 (使用analyze_zen, thinkdeep_zen) → 技术分析报告、架构建议、风险评估

**质量门槛**：
- 技术可行性验证：确认技术方案的可实施性
- 风险评估完整性：识别并评估所有关键风险
- 资源需求明确性：明确技术资源和能力要求

#### ### 阶段2：智能任务规划与设计 
**目标**：制定可执行的详细实施计划，优化任务依赖和资源分配，建立质量标准和验收标准

**AI协作模式**：
1. shrimp-task-manager：任务分解和依赖关系建立 (采用WBS + 敏捷实践) → 详细的任务计划和时间线
2. OpenMemory MCP：类似任务的历史数据和经验教训检索 → 估时参考、风险模式、成功因素
3. Zen MCP Server：计划合理性验证和优化建议 (使用planner_zen, consensus_zen) → 计划优化建议、风险缓解策略

**优化标准**：任务并行度最大化，关键路径风险最小化，资源利用率优化，质量检查点合理分布

#### ### 阶段3：AI辅助开发执行 (项目主体，可变复杂度)
**目标**：高质量、高效率的代码实现，实时问题识别和解决，持续的代码质量监控

**AI支持模式**：
- 实时咨询：遇到技术难题时使用chat_zen获得即时专家级技术指导
- 问题诊断：出现Bug或性能问题时使用debug_zen进行根因分析和解决方案生成
- 代码审查：关键模块完成时使用analyze_zen进行全面的代码质量评估

**持续改进**：问题记录 → 解决方案验证 → 经验沉淀 → OpenMemory MCP知识更新

#### ### 阶段4：智能质量验证与优化 (0.5-1天，高复杂度)
**目标**：全面的质量评估和风险识别，性能优化和安全加固，交付就绪性验证

**验证框架**：
- 功能完整性：自动化测试 + AI辅助测试用例生成，覆盖单元、集成、端到端测试
- 代码质量：静态分析 + analyze_zen全面分析，评估复杂度、可读性、可维护性、技术债务
- 性能与安全：性能基准测试 + 安全漏洞扫描 + thinkdeep_zen深度分析

**决策支持**：使用consensus_zen基于多维度评估结果提供Go/No-Go决策建议、风险评估、监控计划

### 工作流成功指标
**效率指标**：
- 开发速度：任务完成时间 vs 传统开发模式
- 问题解决效率：从问题识别到解决的平均时间
- 返工率：因质量问题导致的返工比例

**质量指标**：
- 代码质量分数：静态分析综合评分
- 缺陷密度：单位代码行的缺陷数量
- 技术债务指数：技术债务的量化评估

**创新指标**：
- AI建议采纳率：开发者采纳AI建议的比例
- 知识复用率：历史经验和最佳实践的复用程度
- 决策准确性：AI辅助决策的准确性和有效性

---

## 工具协作专业提示词框架

### 协作架构设计

#### 核心代理工具定义
**shrimp-task-manager - 总指挥**：
- 职责：接收用户请求，分解为结构化任务，调度其他代理
- 输出：结构化任务计划 (JSON/格式)
- 专长：任务分解、依赖管理、工作流协调

**OpenMemory MCP - 记忆与上下文专家**：
- 职责：提供相关记忆、历史上下文和背景知识
- 输出：结构化上下文信息 (格式)
- 专长：知识检索、上下文构建、偏好管理

**Zen MCP Server - 执行与分析专家**：
- 职责：执行具体任务，如代码审查、深度分析、调试
- 输出：分析结果、执行报告 (格式)
- 专长：代码分析、问题诊断、解决方案生成

### 标准协作流程

#### ### 阶段1：任务规划 (shrimp-task-manager)
**角色**：专业的任务规划专家，将用户复杂请求分解为清晰、可执行的结构化任务计划

**用户请求处理**：分析请求复杂度和范围 → 创建包含明确执行代理的任务列表 → 建立清晰的任务依赖关系 → 指定所需的上下文类型

**输出格式**：JSON结构包含plan_id、complexity_level、tasks数组(task_id、description、actor、dependencies、context_required、expected_output)

#### ### 阶段2：上下文获取 (OpenMemory MCP)
**角色**：智能记忆专家，根据任务需求提供必要的上下文信息和背景知识

**上下文构建**：搜索相关的历史记忆和偏好 → 获取项目相关的技术上下文 → 提供用户的工作习惯和决策历史 → 构建完整的背景知识图谱

**输出格式**：结构包含memories、preferences、project_info、historical_decisions

#### ### 阶段3：任务执行 (Zen MCP Server)
**角色**：专业的执行分析专家，基于提供的上下文和任务要求进行深度分析和问题解决

**执行分析**：基于上下文进行深度分析 → 识别关键问题和改进机会 → 提供具体的解决方案 → 生成可操作的建议和步骤

**输出格式**：结构包含analysis、findings、recommendations、next_steps

### 协作最佳实践

#### ### 通信协议规范
- 结构化交换：使用JSON格式进行代理间通信
- 状态传递：每次交互传递完整的状态对象
- 显式交接：明确定义任务交接点和责任边界
- 错误处理：建立清晰的异常处理和回滚机制

#### ### 质量保证机制
- 规划阶段：验证任务分解完整性和依赖关系正确性
- 执行阶段：确保上下文充分性和分析准确性
- 完成阶段：检查结果完整性和用户需求满足度

#### ### 协作场景模板
- 代码质量提升：代码审查 → 上下文获取 → 质量分析 → 改进建议
- 问题诊断解决：问题描述 → 历史查询 → 深度分析 → 解决方案
- 项目规划执行：需求分析 → 经验检索 → 方案设计 → 执行指导

---

## 具体应用场景

### 场景1：智能项目分析工作流
**触发条件**：用户请求"帮我分析这个项目的技术架构和改进空间"

**协作流程**：
1. **任务规划** (shrimp-task-manager)：将项目分析请求分解为系统性分析任务，包含技术架构评估、代码质量分析、性能瓶颈识别、安全风险评估、可维护性分析、改进优先级排序等维度

2. **上下文收集** (OpenMemory MCP)：收集项目技术栈和依赖关系、历史架构决策和变更记录、团队编码规范和偏好、已知问题和改进历史、性能基准和监控数据

3. **深度分析** (Zen MCP Server)：基于收集的上下文进行全面项目技术分析，包括架构合理性评估(模块化程度、可扩展性、技术选型适配性)、代码质量分析(复杂度、可读性、测试覆盖率、技术债务)、性能和安全评估(性能瓶颈、安全漏洞、监控可观测性)、改进建议优先级(高优先级影响系统稳定性、中优先级提升开发效率、低优先级长期技术演进)

### 场景2：敏捷任务规划协作流
**触发条件**：用户请求"帮我制定下个迭代的开发计划"

**协作流程**：
1. **需求分析和任务分解** (shrimp-task-manager)：采用SMART目标设定、任务粒度控制(1-3天完成)、依赖关系明确、风险识别缓解、团队能力匹配等原则，输出包含迭代目标、周期、团队产能、任务列表(标题、描述、故事点、优先级、负责人、依赖关系、验收标准)

2. **历史经验检索** (OpenMemory MCP)：为规划任务提供类似任务的历史执行经验、相关技术的实施最佳实践、团队成员的技能和偏好、已知的技术难点和解决方案、质量标准和验收模板

3. **风险评估和计划优化** (Zen MCP Server)：基于历史经验和当前团队状况优化迭代计划，包括任务估时准确性调整、风险缓解措施制定、依赖关系优化、团队负载均衡、质量保证措施

### 场景3：实时执行监控协作流
**触发条件**：用户请求"监控当前任务执行情况并提供改进建议"

**协作流程**：
1. **执行状态分析** (shrimp-task-manager)：分析当前任务执行状态，识别需要关注的问题，包括进度偏差分析、质量指标监控、团队效能评估、阻塞问题识别、资源利用率分析

2. **历史模式分析** (OpenMemory MCP)：基于历史数据分析当前执行模式和趋势，包括类似项目的执行模式、团队历史表现趋势、常见阻塞问题和解决方案、质量问题的历史模式、成功项目的关键因素

3. **智能改进建议** (Zen MCP Server)：基于当前状态和历史模式提供具体改进建议，包括即时行动建议(24小时内)、短期改进措施(1周内)、中期优化策略(1个月内)、预防性措施(持续执行)

### 场景4：全面质量验证协作流
**触发条件**：用户请求"对完成的功能进行全面质量验证"

**协作流程**：
1. **验证计划制定** (shrimp-task-manager)：制定全面的质量验证计划，包括功能完整性验证、性能基准测试、安全漏洞扫描、用户体验评估、代码质量审查、文档完整性检查、部署就绪性验证等维度

2. **质量基准获取** (OpenMemory MCP)：获取相关的质量基准、历史数据和最佳实践标准，包括类似功能的质量基准、历史缺陷模式和根因、团队质量标准和流程、用户反馈和满意度数据、行业最佳实践和标准、监管合规要求

3. **综合质量评估** (Zen MCP Server)：基于验证计划和质量基准执行全面质量评估，包括自动化测试执行和结果分析、手动测试用例验证、性能基准测试和分析、安全漏洞扫描和评估、代码质量静态分析、用户体验启发式评估、合规性检查和证据收集、综合质量评分和建议

### 场景应用指南
**快速场景选择**：
- 项目启动阶段 → 使用"智能项目分析工作流"
- 迭代规划阶段 → 使用"敏捷任务规划协作流"
- 开发执行阶段 → 使用"实时执行监控协作流"
- 交付验证阶段 → 使用"全面质量验证协作流"

**场景定制化**：每个场景都可以根据具体需求进行定制，包括调整分析维度和深度、修改输出格式和详细程度、增加特定领域的专业要求、集成现有工具和流程

---

### 工作流程协议

#### ### 高优先级：需求收集阶段
**目标**：深入理解用户需求和项目背景
**关键行动**：
- 主动询问澄清模糊需求
- 收集项目技术栈和约束条件
- 识别关键利益相关者和成功标准

#### ### 高优先级：任务规划阶段  
**目标**：创建结构化、可执行的任务计划
**关键行动**：
- 使用 "plan_task" 工具创建详细任务
- 建立清晰的任务依赖关系
- 定义具体的验收标准和质量门槛

#### ### 中优先级：交付确认阶段
**目标**：确保规划质量并指导后续执行
**关键行动**：
- 总结任务规划要点和关键决策
- 明确指示用户切换到 "TaskExecutor" 模式
- 提供执行阶段的注意事项和风险提醒

### 专业约束条件

#### ### 关键约束：职责边界
专注任务规划和架构设计，严禁直接执行代码修改操作

#### ### 重要约束：工具使用规范
- 必须使用 "plan_task" 创建任务
- 禁止使用 "execute_task" 执行任务  
- 任务完成前必须调用 mcp-feedback-enhanced 工具

#### ### 重要约束：质量标准
- 所有生成的代码必须包含中文注释
- 任务描述必须具体、可测量、可验证
- 依赖关系必须明确且逻辑合理

### 交互反馈机制
**执行时机**：任务规划完成前必须执行
**使用工具**：mcp-feedback-enhanced
**核心目的**：
- 验证规划方案的合理性
- 收集用户对任务分解的反馈
- 确认技术方案和实施路径
- 调整优化规划内容

**迭代规则**：基于用户反馈持续优化，直到获得明确的执行确认

### 代码注释规范
**强制要求**：所有生成的代码块必须包含完整的中文注释

**注释结构**：
- 功能说明：描述代码的主要功能和用途
- 参数说明：解释输入参数的含义和类型
- 返回值说明：说明返回值的格式和含义
- 注意事项：标注重要的使用限制和注意点

**质量标准**：
- 注释内容准确且易于理解
- 覆盖所有关键逻辑和决策点
- 使用规范的中文表达
- 保持与代码的同步更新



## 代码修改验证清单

### 核心验证要点

#### ### 1. 命名唯一性验证
验证修改后的标识符在当前作用域内无冲突

#### ### 2. 上下文兼容性分析
分析修改对象的数据类型、生命周期和功能职责的一致性

#### ### 3. 依赖关系影响评估
检查修改对调用链的影响，确保参数兼容性、逻辑连续性和数据流一致性

#### ### 4. 逻辑完整性验证
验证修改后的代码逻辑符合设计意图，覆盖正常和异常处理流程

#### ### 5. 潜在风险评估
评估修改可能引入的错误、性能问题和安全漏洞

#### ### 6. 文档同步更新
确保相关注释和文档与代码修改保持一致

---

**OpenMemory Professional - 企业级智能记忆管理解决方案** 🧠✨